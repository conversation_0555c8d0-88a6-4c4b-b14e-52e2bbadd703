#!/usr/bin/env python3
"""
Telegram Auto-Responder с использованием Telethon и LLM
"""

import asyncio
import os
import json
import sqlite3
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional
import logging

from telethon import TelegramClient, events
from telethon.tl.types import User, Chat, Channel
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import uvicorn
from dotenv import load_dotenv
from llm_providers import get_llm_provider
from config import TELEGRAM_CONFIG, OPENROUTER_CONFIG, AUTORESPONDER_CONFIG, SERVER_CONFIG

# Загружаем переменные окружения
load_dotenv()

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TelegramAutoResponder:
    def __init__(self):
        # Telegram API credentials из config.py
        self.api_id = TELEGRAM_CONFIG['api_id']
        self.api_hash = TELEGRAM_CONFIG['api_hash']
        self.phone = TELEGRAM_CONFIG['phone']
        self.session_name = TELEGRAM_CONFIG['session_name']

        # Проверяем, настроены ли реальные ключи
        self.demo_mode = (
            not self.api_id or
            not self.api_hash or
            str(self.api_id) == 'your_api_id' or
            self.api_hash == 'your_api_hash'
        )

        # LLM провайдер
        self.llm_provider = get_llm_provider()

        # Инициализация клиента Telegram (только если не демо режим)
        if not self.demo_mode:
            self.client = TelegramClient(self.session_name, self.api_id, self.api_hash)
            logger.info(f"Инициализация Telegram клиента для номера {self.phone}")
        else:
            self.client = None
            logger.info("Запуск в демо-режиме (Telegram API не настроен)")
        
        # Активные чаты для автоответов
        self.active_chats: Dict[int, bool] = {}
        
        # Контекст для каждого чата
        self.chat_contexts: Dict[int, List[Dict]] = {}
        
        # Максимальный размер контекста
        self.max_context_size = 20
        
        # WebSocket соединения для уведомлений
        self.websocket_connections: List[WebSocket] = []

        # Настройка логирования с отправкой в WebSocket
        self.setup_websocket_logging()
        
        # Инициализация базы данных
        self.init_database()

    def setup_websocket_logging(self):
        """Настройка отправки логов через WebSocket"""
        class WebSocketHandler(logging.Handler):
            def __init__(self, auto_responder):
                super().__init__()
                self.auto_responder = auto_responder

            def emit(self, record):
                try:
                    log_data = {
                        'type': 'log',
                        'level': record.levelname.lower(),
                        'message': record.getMessage(),
                        'timestamp': record.created * 1000,  # JavaScript timestamp
                        'chat_id': getattr(record, 'chat_id', None)
                    }
                    asyncio.create_task(self.auto_responder.notify_websockets(log_data))
                except:
                    pass

        # Добавляем WebSocket handler к логгеру
        websocket_handler = WebSocketHandler(self)
        websocket_handler.setLevel(logging.INFO)
        logger.addHandler(websocket_handler)
    
    def init_database(self):
        """Инициализация SQLite базы данных"""
        conn = sqlite3.connect('autoresponder.db')
        cursor = conn.cursor()
        
        # Таблица для настроек чатов
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS chat_settings (
                chat_id INTEGER PRIMARY KEY,
                chat_title TEXT,
                is_active BOOLEAN DEFAULT FALSE,
                context_reset_interval INTEGER DEFAULT 3600,
                last_context_reset TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Таблица для истории сообщений
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS message_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                chat_id INTEGER,
                message_text TEXT,
                is_outgoing BOOLEAN,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (chat_id) REFERENCES chat_settings (chat_id)
            )
        ''')
        
        conn.commit()
        conn.close()
    
    async def start_client(self):
        """Запуск Telegram клиента"""
        if self.demo_mode:
            logger.info("Демо-режим: Telegram клиент не запускается")
            return

        await self.client.start(phone=self.phone)
        logger.info("Telegram клиент запущен")

        # Регистрация обработчика новых сообщений
        @self.client.on(events.NewMessage)
        async def handle_new_message(event):
            await self.process_new_message(event)
    
    async def process_new_message(self, event):
        """Обработка нового сообщения"""
        chat_id = event.chat_id
        message_text = event.message.message

        # Логируем все входящие сообщения для отладки
        logger.info(f"Получено сообщение от чата {chat_id}: {message_text[:50]}{'...' if len(message_text) > 50 else ''}")
        logger.info(f"Состояние автоответчика для чата {chat_id}: {self.active_chats.get(chat_id, False)}")
        logger.info(f"Исходящее сообщение: {event.message.out}")

        # Проверяем, активен ли автоответчик для этого чата
        if chat_id not in self.active_chats or not self.active_chats[chat_id]:
            logger.info(f"Автоответчик для чата {chat_id} неактивен, пропускаем")
            return

        # Игнорируем собственные сообщения
        if event.message.out:
            logger.info(f"Пропускаем собственное сообщение в чате {chat_id}")
            return
        
        # Логируем входящее сообщение с привязкой к чату
        log_record = logger.makeRecord(
            logger.name, logging.INFO, __file__, 0,
            f"Получено сообщение: {message_text[:50]}{'...' if len(message_text) > 50 else ''}",
            (), None
        )
        log_record.chat_id = chat_id
        logger.handle(log_record)
        
        # Сохраняем сообщение в контекст
        await self.add_to_context(chat_id, message_text, is_outgoing=False)
        
        # Генерируем ответ
        response = await self.generate_response(chat_id, message_text)
        
        if response:
            # Отправляем ответ
            if not self.demo_mode:
                await self.client.send_message(chat_id, response)

            # Сохраняем ответ в контекст
            await self.add_to_context(chat_id, response, is_outgoing=True)

            # Уведомляем веб-интерфейс
            await self.notify_websockets({
                'type': 'message_sent',
                'chat_id': chat_id,
                'message': response
            })

            # Логируем с привязкой к чату
            log_record = logger.makeRecord(
                logger.name, logging.INFO, __file__, 0,
                f"Отправлен ответ: {response[:50]}{'...' if len(response) > 50 else ''}",
                (), None
            )
            log_record.chat_id = chat_id
            logger.handle(log_record)

            # Проверяем, что автоответчик все еще активен после отправки
            logger.info(f"После отправки ответа - состояние автоответчика для чата {chat_id}: {self.active_chats.get(chat_id, False)}")
        else:
            logger.warning(f"Не удалось сгенерировать ответ для чата {chat_id}")
    
    async def add_to_context(self, chat_id: int, message: str, is_outgoing: bool):
        """Добавление сообщения в контекст чата"""
        if chat_id not in self.chat_contexts:
            self.chat_contexts[chat_id] = []
        
        context_entry = {
            'message': message,
            'is_outgoing': is_outgoing,
            'timestamp': datetime.now().isoformat()
        }
        
        self.chat_contexts[chat_id].append(context_entry)
        
        # Ограничиваем размер контекста
        if len(self.chat_contexts[chat_id]) > self.max_context_size:
            self.chat_contexts[chat_id] = self.chat_contexts[chat_id][-self.max_context_size:]
        
        # Сохраняем в базу данных
        conn = sqlite3.connect('autoresponder.db')
        cursor = conn.cursor()
        cursor.execute(
            'INSERT INTO message_history (chat_id, message_text, is_outgoing) VALUES (?, ?, ?)',
            (chat_id, message, is_outgoing)
        )
        conn.commit()
        conn.close()
    
    async def generate_response(self, chat_id: int, message: str) -> Optional[str]:
        """Генерация ответа с помощью LLM"""
        try:
            # Получаем контекст чата
            context = self.chat_contexts.get(chat_id, [])

            # Формируем контекст для LLM
            context_text = ""
            for entry in context[-10:]:  # Последние 10 сообщений
                role = "Я" if entry['is_outgoing'] else "Собеседник"
                context_text += f"{role}: {entry['message']}\n"

            # Генерируем ответ через LLM провайдер
            response = await self.llm_provider.generate_response(context, context_text)

            return response

        except Exception as e:
            logger.error(f"Ошибка генерации ответа: {e}")
            return None
    
    async def get_chats(self) -> List[Dict]:
        """Получение списка доступных чатов"""
        if self.demo_mode:
            # Возвращаем демо-чаты
            return [
                {
                    'id': 1,
                    'title': 'Демо чат 1',
                    'type': 'User',
                    'is_active': self.active_chats.get(1, False)
                },
                {
                    'id': 2,
                    'title': 'Демо группа',
                    'type': 'Chat',
                    'is_active': self.active_chats.get(2, False)
                },
                {
                    'id': 3,
                    'title': 'Демо канал',
                    'type': 'Channel',
                    'is_active': self.active_chats.get(3, False)
                }
            ]

        chats = []
        async for dialog in self.client.iter_dialogs():
            if isinstance(dialog.entity, (User, Chat, Channel)):
                chats.append({
                    'id': dialog.entity.id,
                    'title': dialog.title,
                    'type': type(dialog.entity).__name__,
                    'is_active': self.active_chats.get(dialog.entity.id, False)
                })
        return chats
    
    async def toggle_chat_autoresponder(self, chat_id: int, active: bool):
        """Включение/выключение автоответчика для чата"""
        self.active_chats[chat_id] = active

        # Сохраняем в базу данных
        conn = sqlite3.connect('autoresponder.db')
        cursor = conn.cursor()
        cursor.execute(
            'INSERT OR REPLACE INTO chat_settings (chat_id, is_active) VALUES (?, ?)',
            (chat_id, active)
        )
        conn.commit()
        conn.close()

        status = 'включен' if active else 'выключен'
        log_record = logger.makeRecord(
            logger.name, logging.INFO, __file__, 0,
            f"Автоответчик для чата {chat_id}: {status}",
            (), None
        )
        log_record.chat_id = chat_id
        logger.handle(log_record)
    
    async def notify_websockets(self, data: Dict):
        """Отправка уведомлений через WebSocket"""
        if self.websocket_connections:
            message = json.dumps(data)
            for websocket in self.websocket_connections[:]:
                try:
                    await websocket.send_text(message)
                except:
                    self.websocket_connections.remove(websocket)

# Глобальный экземпляр автоответчика
auto_responder = TelegramAutoResponder()

# FastAPI приложение
app = FastAPI(title="Telegram Auto-Responder API")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.on_event("startup")
async def startup_event():
    """Запуск при старте приложения"""
    await auto_responder.start_client()

@app.get("/api/chats")
async def get_chats():
    """Получение списка чатов"""
    return await auto_responder.get_chats()

@app.post("/api/chats/{chat_id}/toggle")
async def toggle_chat(chat_id: int, active: bool):
    """Включение/выключение автоответчика для чата"""
    await auto_responder.toggle_chat_autoresponder(chat_id, active)
    return {"success": True}

@app.post("/api/demo/simulate_message")
async def simulate_message(chat_id: int, message: str):
    """Демо-функция для симуляции получения сообщения (только в демо-режиме)"""
    if not auto_responder.demo_mode:
        raise HTTPException(status_code=400, detail="Доступно только в демо-режиме")

    # Проверяем, активен ли автоответчик для этого чата
    if chat_id not in auto_responder.active_chats or not auto_responder.active_chats[chat_id]:
        raise HTTPException(status_code=400, detail="Автоответчик не активен для этого чата")

    # Симулируем обработку сообщения
    await auto_responder.add_to_context(chat_id, message, is_outgoing=False)

    # Логируем входящее сообщение
    log_record = logger.makeRecord(
        logger.name, logging.INFO, __file__, 0,
        f"[ДЕМО] Получено сообщение: {message[:50]}{'...' if len(message) > 50 else ''}",
        (), None
    )
    log_record.chat_id = chat_id
    logger.handle(log_record)

    # Генерируем ответ
    response = await auto_responder.generate_response(chat_id, message)

    if response:
        # Сохраняем ответ в контекст
        await auto_responder.add_to_context(chat_id, response, is_outgoing=True)

        # Уведомляем веб-интерфейс
        await auto_responder.notify_websockets({
            'type': 'message_sent',
            'chat_id': chat_id,
            'message': response
        })

        # Логируем ответ
        log_record = logger.makeRecord(
            logger.name, logging.INFO, __file__, 0,
            f"[ДЕМО] Отправлен ответ: {response[:50]}{'...' if len(response) > 50 else ''}",
            (), None
        )
        log_record.chat_id = chat_id
        logger.handle(log_record)

    return {"status": "success", "response": response}

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket для real-time уведомлений"""
    await websocket.accept()
    auto_responder.websocket_connections.append(websocket)
    
    try:
        while True:
            await websocket.receive_text()
    except WebSocketDisconnect:
        auto_responder.websocket_connections.remove(websocket)

if __name__ == "__main__":
    uvicorn.run(app,
                host=SERVER_CONFIG['host'],
                port=SERVER_CONFIG['port'])
