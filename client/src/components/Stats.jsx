import React, { useState } from 'react'

const Stats = ({ stats }) => {
  const [isCollapsed, setIsCollapsed] = useState(false)

  return (
    <div className="stats">
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '10px' }}>
        <h3 style={{ margin: 0, fontSize: '16px' }}>Статистика</h3>
        <button
          onClick={() => setIsCollapsed(!isCollapsed)}
          style={{
            fontSize: '12px',
            padding: '5px 10px',
            background: 'none',
            border: '1px solid #ddd',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          {isCollapsed ? '📊 Показать' : '📁 Скрыть'}
        </button>
      </div>

      {!isCollapsed && (
        <div style={{ display: 'flex', gap: '15px', flexWrap: 'wrap' }}>
          <div className="stat-card">
            <div className="stat-value">{stats.totalChats}</div>
            <div className="stat-label">Всего чатов</div>
          </div>

          <div className="stat-card">
            <div className="stat-value">{stats.activeChats}</div>
            <div className="stat-label">Активных чатов</div>
          </div>

          <div className="stat-card">
            <div className="stat-value">{stats.messagesSent}</div>
            <div className="stat-label">Сообщений отправлено</div>
          </div>
        </div>
      )}
    </div>
  )
}

export default Stats
