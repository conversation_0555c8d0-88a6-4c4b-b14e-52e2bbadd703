#!/usr/bin/env python3
"""
Безопасные LLM провайдеры для Telegram автоответчика
Принципы безопасности:
1. Никакого сохранения истории
2. Только последнее сообщение в LLM
3. Прозрачность - всегда указывать что это автоответчик
4. Минимальный контекст
"""

import os
import asyncio
import aiohttp
import json
from typing import Optional
from abc import ABC, abstractmethod

class SecureLLMProvider(ABC):
    """Базовый класс для безопасных LLM провайдеров"""
    
    @abstractmethod
    async def generate_response(self, last_message: str, chat_context: str = "") -> Optional[str]:
        """
        Безопасная генерация ответа - только на последнее сообщение
        
        Args:
            last_message: Последнее сообщение от собеседника
            chat_context: Минимальный контекст чата (например, "дружеский чат")
            
        Returns:
            Сгенерированный ответ с префиксом автоответчика или None в случае ошибки
        """
        pass

class SecureOpenRouterProvider(SecureLLMProvider):
    """Безопасный OpenRouter API провайдер"""
    
    def __init__(self, api_key: str, model: str = "deepseek/deepseek-chat"):
        self.api_key = api_key
        self.model = model
        self.base_url = "https://openrouter.ai/api/v1"
    
    async def generate_response(self, last_message: str, chat_context: str = "") -> Optional[str]:
        """
        Безопасная генерация ответа - только на последнее сообщение
        """
        try:
            # Безопасный системный промпт
            system_prompt = """Ты автоответчик в Telegram чате. 
ВАЖНО: Всегда начинай ответ с "🤖 Автоответчик: " чтобы собеседник знал, что отвечает ИИ.
Отвечай кратко, дружелюбно и естественно на русском языке.
Не запрашивай дополнительную информацию и не ссылайся на предыдущие сообщения."""

            # Формируем сообщения для API
            messages = [
                {"role": "system", "content": system_prompt}
            ]
            
            # Добавляем минимальный контекст если есть
            if chat_context:
                messages.append({"role": "system", "content": f"Контекст: {chat_context}"})
            
            # Добавляем только последнее сообщение
            messages.append({"role": "user", "content": last_message})

            payload = {
                "model": self.model,
                "messages": messages,
                "max_tokens": 150,  # Ограничиваем длину ответа
                "temperature": 0.7,
                "stream": False
            }
            
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json",
                "HTTP-Referer": "http://localhost:3000",
                "X-Title": "Secure Telegram Auto-Responder"
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(f"{self.base_url}/chat/completions", headers=headers, json=payload) as response:
                    if response.status == 200:
                        data = await response.json()
                        response_text = data['choices'][0]['message']['content'].strip()
                        
                        # Убеждаемся что ответ начинается с префикса автоответчика
                        if not response_text.startswith("🤖"):
                            response_text = f"🤖 Автоответчик: {response_text}"
                        
                        return response_text
                    else:
                        error_text = await response.text()
                        print(f"🔒 Secure OpenRouter API error: {response.status} - {error_text}")
                        return None
                        
        except Exception as e:
            print(f"🔒 Ошибка Secure OpenRouter API: {e}")
            return None

class SecureMockProvider(SecureLLMProvider):
    """Безопасная заглушка для тестирования"""
    
    async def generate_response(self, last_message: str, chat_context: str = "") -> Optional[str]:
        """Безопасная mock генерация ответа"""
        
        last_message_lower = last_message.lower()
        
        # Простые ответы на основе ключевых слов
        if any(word in last_message_lower for word in ['привет', 'hello', 'hi']):
            return "🤖 Автоответчик: Привет! Как дела?"
        elif any(word in last_message_lower for word in ['как дела', 'как ты', 'how are you']):
            return "🤖 Автоответчик: Всё отлично, спасибо! А у тебя как?"
        elif any(word in last_message_lower for word in ['пока', 'bye', 'goodbye']):
            return "🤖 Автоответчик: Пока! Удачи!"
        elif '?' in last_message:
            return "🤖 Автоответчик: Интересный вопрос! Дай подумать..."
        else:
            return f"🤖 Автоответчик: Понял! Ты написал: '{last_message[:30]}...'"

def get_secure_llm_provider() -> SecureLLMProvider:
    """Безопасная фабрика для создания LLM провайдера"""
    
    provider_type = os.getenv('LLM_PROVIDER', 'mock').lower()
    
    if provider_type == 'openrouter':
        api_key = os.getenv('OPENROUTER_API_KEY')
        model = os.getenv('OPENROUTER_MODEL', 'deepseek/deepseek-chat')
        if not api_key:
            print("🔒 OPENROUTER_API_KEY не найден, используется безопасная заглушка")
            return SecureMockProvider()
        return SecureOpenRouterProvider(api_key, model)
    
    else:
        print(f"🔒 Используется безопасная заглушка LLM провайдера (provider: {provider_type})")
        return SecureMockProvider()
