import React from 'react'

const ChatList = ({ chats, loading, onToggleChat, onRefresh }) => {
  if (loading) {
    return (
      <div className="chat-list">
        <div className="loading">Загрузка чатов...</div>
      </div>
    )
  }

  if (!chats || chats.length === 0) {
    return (
      <div className="chat-list">
        <div className="loading">
          Чаты не найдены.
          <button onClick={onRefresh} style={{ marginLeft: '10px' }}>
            Обновить
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="chat-list">
      <div style={{ padding: '15px 20px', borderBottom: '1px solid #eee', background: '#f8f9fa' }}>
        <h3>Доступные чаты ({chats.length})</h3>
        <button onClick={onRefresh} style={{ marginLeft: '10px', fontSize: '12px' }}>
          🔄 Обновить
        </button>
      </div>
      
      {chats.map(chat => {
        const isActive = chat.active || chat.is_active;
        return (
          <div key={chat.id} className="chat-item">
            <div className="chat-info">
              <div className="chat-title">{chat.name || chat.title}</div>
              <div className="chat-type">{chat.type}</div>
            </div>

            <div className="chat-controls">
              <div
                className={`status-indicator ${isActive ? 'active' : ''}`}
                title={isActive ? 'Активен' : 'Неактивен'}
              />

              <button
                className={`toggle-button ${isActive ? 'active' : 'inactive'}`}
                onClick={() => onToggleChat(chat.id, isActive)}
              >
                {isActive ? 'Выключить' : 'Включить'}
              </button>
            </div>
          </div>
        )
      })}
    </div>
  )
}

export default ChatList
