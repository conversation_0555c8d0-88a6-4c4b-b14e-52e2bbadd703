{"name": "telegram-auto-responder", "version": "1.0.0", "description": "Автоответчик для Telegram чатов с LLM интеграцией", "main": "server/index.js", "scripts": {"dev": "concurrently \"npm run server:dev\" \"npm run client:dev\"", "server:dev": "cd server && python main.py", "client:dev": "cd client && npm run dev", "build": "cd client && npm run build", "start": "cd server && python main.py", "install:all": "npm install && cd server && pip install -r requirements.txt && cd ../client && npm install", "setup": "npm run install:all"}, "keywords": ["telegram", "bot", "llm", "auto-responder"], "author": "Your Name", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}