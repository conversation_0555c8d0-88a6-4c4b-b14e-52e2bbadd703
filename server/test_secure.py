#!/usr/bin/env python3
"""
Тест безопасной версии автоответчика
"""
import asyncio
import os
from dotenv import load_dotenv
from secure_llm_providers import get_secure_llm_provider

# Загружаем переменные окружения
load_dotenv()

async def test_secure_system():
    """Тестирует безопасную систему"""
    
    print("🔒 Тестирование безопасной системы автоответчика")
    print("=" * 60)
    
    # Получаем провайдера
    provider = get_secure_llm_provider()
    print(f"🔒 Используется провайдер: {type(provider).__name__}")
    print(f"🔒 LLM Provider: {os.getenv('LLM_PROVIDER')}")
    print(f"🔒 Model: {os.getenv('OPENROUTER_MODEL')}")
    print()
    
    # Тестовые сообщения
    test_messages = [
        "Привет! Как дела?",
        "Что нового?",
        "Расскажи анекдот",
        "Как погода?",
        "Пока!"
    ]
    
    print("📝 Тестируем безопасную генерацию ответов:")
    print("-" * 40)
    
    for i, message in enumerate(test_messages, 1):
        print(f"\n{i}. Тест сообщения: '{message}'")
        
        try:
            # Безопасная генерация - только последнее сообщение
            response = await provider.generate_response(message, "дружеский чат")
            
            if response:
                print(f"   ✅ Ответ: {response}")
                
                # Проверяем что ответ содержит префикс автоответчика
                if "🤖" in response:
                    print("   ✅ Префикс автоответчика присутствует")
                else:
                    print("   ⚠️  Префикс автоответчика отсутствует")
            else:
                print("   ❌ Пустой ответ")
                
        except Exception as e:
            print(f"   ❌ Ошибка: {e}")
    
    print("\n" + "=" * 60)
    print("🔒 Принципы безопасности:")
    print("✅ Никакой истории сообщений не сохраняется")
    print("✅ LLM видит только последнее сообщение")
    print("✅ Всегда указывается что это автоответчик")
    print("✅ Минимальный контекст")
    print("✅ Подробное логирование")

if __name__ == "__main__":
    asyncio.run(test_secure_system())
