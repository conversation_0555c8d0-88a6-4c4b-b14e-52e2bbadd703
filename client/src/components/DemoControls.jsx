import React, { useState } from 'react'
import axios from 'axios'

const DemoCont<PERSON>s = ({ chats, onLogMessage }) => {
  const [selectedChatId, setSelectedChatId] = useState('')
  const [message, setMessage] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  const activeChats = chats.filter(chat => chat.is_active)

  const handleSimulateMessage = async (e) => {
    e.preventDefault()
    
    if (!selectedChatId || !message.trim()) {
      onLogMessage('warning', 'Выберите чат и введите сообщение')
      return
    }

    setIsLoading(true)
    try {
      const response = await axios.post('/api/demo/simulate_message', null, {
        params: {
          chat_id: parseInt(selectedChatId),
          message: message.trim()
        }
      })
      
      onLogMessage('info', `Демо-сообщение отправлено в чат ${selectedChatId}`)
      setMessage('')
    } catch (error) {
      const errorMsg = error.response?.data?.detail || error.message
      onLogMessage('error', `Ошибка симуляции: ${errorMsg}`)
    } finally {
      setIsLoading(false)
    }
  }

  const predefinedMessages = [
    "Привет! Как дела?",
    "Что нового?",
    "Расскажи анекдот",
    "Какая погода сегодня?",
    "Помоги с задачей по математике"
  ]

  return (
    <div className="demo-controls">
      <div className="demo-header">
        <h3>🧪 Демо-режим</h3>
        <p>Симуляция получения сообщений для тестирования автоответчика</p>
      </div>
      
      <form onSubmit={handleSimulateMessage} className="demo-form">
        <div className="form-group">
          <label htmlFor="chat-select">Выберите активный чат:</label>
          <select
            id="chat-select"
            value={selectedChatId}
            onChange={(e) => setSelectedChatId(e.target.value)}
            disabled={activeChats.length === 0}
            required
          >
            <option value="">-- Выберите чат --</option>
            {activeChats.map(chat => (
              <option key={chat.id} value={chat.id}>
                {chat.title} ({chat.type})
              </option>
            ))}
          </select>
          {activeChats.length === 0 && (
            <small className="no-active-chats">
              Нет активных чатов. Включите автоответчик для хотя бы одного чата.
            </small>
          )}
        </div>

        <div className="form-group">
          <label htmlFor="message-input">Сообщение:</label>
          <textarea
            id="message-input"
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            placeholder="Введите сообщение для симуляции..."
            rows={3}
            required
          />
        </div>

        <div className="predefined-messages">
          <label>Быстрые сообщения:</label>
          <div className="message-buttons">
            {predefinedMessages.map((msg, index) => (
              <button
                key={index}
                type="button"
                className="predefined-message-btn"
                onClick={() => setMessage(msg)}
              >
                {msg}
              </button>
            ))}
          </div>
        </div>

        <button
          type="submit"
          className="simulate-btn"
          disabled={isLoading || !selectedChatId || !message.trim()}
        >
          {isLoading ? 'Отправка...' : '📤 Симулировать сообщение'}
        </button>
      </form>
    </div>
  )
}

export default DemoControls
