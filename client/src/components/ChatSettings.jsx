import React, { useState, useEffect } from 'react';

const ChatSettings = ({ chatId, chatTitle, onClose }) => {
  const [settings, setSettings] = useState({
    welcome_message: '',
    system_prompt: ''
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    if (chatId) {
      loadSettings();
    }
  }, [chatId]);

  const loadSettings = async () => {
    try {
      const response = await fetch(`/api/chats/${chatId}/settings`);
      if (response.ok) {
        const data = await response.json();
        console.log('Загружены настройки:', data);
        setSettings(data);
      }
    } catch (error) {
      console.error('Ошибка загрузки настроек:', error);
    } finally {
      setLoading(false);
    }
  };

  const saveSettings = async () => {
    setSaving(true);
    console.log('Сохраняем настройки:', settings);
    try {
      const response = await fetch(`/api/chats/${chatId}/settings`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings),
      });

      if (response.ok) {
        alert('Настройки сохранены!');
        // Перезагружаем настройки чтобы убедиться что они сохранились
        await loadSettings();
        onClose();
      } else {
        const errorText = await response.text();
        console.error('Ошибка сохранения:', errorText);
        alert('Ошибка сохранения настроек: ' + errorText);
      }
    } catch (error) {
      console.error('Ошибка сохранения:', error);
      alert('Ошибка сохранения настроек');
    } finally {
      setSaving(false);
    }
  };

  const handleInputChange = (field, value) => {
    setSettings(prev => ({
      ...prev,
      [field]: value
    }));
  };

  if (loading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 w-full max-w-2xl">
          <div className="text-center">Загрузка настроек...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-bold">Настройки чата</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 text-2xl"
          >
            ×
          </button>
        </div>

        <div className="mb-4">
          <h3 className="font-semibold text-gray-700 mb-2">Чат: {chatTitle}</h3>
          <p className="text-sm text-gray-500 mb-4">ID: {chatId}</p>
        </div>

        <div className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Приветственное сообщение
            </label>
            <textarea
              value={settings.welcome_message}
              onChange={(e) => handleInputChange('welcome_message', e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              rows="3"
              placeholder="Введите приветственное сообщение..."
            />
            <p className="text-xs text-gray-500 mt-1">
              Это сообщение будет отправлено первым при активации автоответчика
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Системный промпт для ИИ
            </label>
            <textarea
              value={settings.system_prompt}
              onChange={(e) => handleInputChange('system_prompt', e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              rows="6"
              placeholder="Введите инструкции для ИИ..."
            />
            <p className="text-xs text-gray-500 mt-1">
              Эти инструкции определяют, как ИИ будет отвечать в этом чате
            </p>
          </div>
        </div>

        <div className="flex justify-end space-x-3 mt-6 pt-4 border-t">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
            disabled={saving}
          >
            Отмена
          </button>
          <button
            onClick={saveSettings}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
            disabled={saving}
          >
            {saving ? 'Сохранение...' : 'Сохранить'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ChatSettings;
