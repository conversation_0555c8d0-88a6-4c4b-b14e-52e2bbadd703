{"name": "telegram-auto-responder", "version": "1.0.0", "description": "Автоответчик для Telegram чатов с LLM интеграцией", "main": "server/index.js", "scripts": {"dev": "concurrently \"npm run server:dev\" \"npm run client:dev\"", "server:dev": "cd server && npm run dev", "client:dev": "cd client && npm run dev", "build": "cd client && npm run build", "start": "cd server && npm start", "install:all": "npm install && cd server && npm install && cd ../client && npm install"}, "keywords": ["telegram", "bot", "llm", "auto-responder"], "author": "Your Name", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}