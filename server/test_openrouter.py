#!/usr/bin/env python3
"""
Тест OpenRouter API
"""
import asyncio
import os
from dotenv import load_dotenv
from llm_providers import get_llm_provider

# Загружаем переменные окружения из .env файла
load_dotenv()

async def test_openrouter():
    """Тестирует OpenRouter API"""
    
    print("🧪 Тестирование OpenRouter API...")
    print(f"Провайдер: {os.getenv('LLM_PROVIDER')}")
    print(f"Модель: {os.getenv('OPENROUTER_MODEL')}")
    api_key = os.getenv('OPENROUTER_API_KEY')
    if api_key:
        print(f"API ключ: {api_key[:20]}...")
    else:
        print("API ключ: НЕ НАЙДЕН")
    print()
    
    # Получаем провайдера
    provider = get_llm_provider()
    print(f"Используется провайдер: {type(provider).__name__}")
    
    # Тестовые сообщения
    test_messages = [
        {"message": "Привет! Как дела?", "is_outgoing": False, "timestamp": "2025-01-01 12:00:00"}
    ]
    
    print("\n📝 Отправляем тестовое сообщение...")
    
    try:
        response = await provider.generate_response(test_messages, "Дружеский чат")
        
        if response:
            print(f"✅ Успех! Ответ от OpenRouter:")
            print(f"📤 Вопрос: {test_messages[0]['message']}")
            print(f"📥 Ответ: {response}")
        else:
            print("❌ Ошибка: Пустой ответ от API")
            
    except Exception as e:
        print(f"❌ Ошибка при тестировании: {e}")

if __name__ == "__main__":
    asyncio.run(test_openrouter())
