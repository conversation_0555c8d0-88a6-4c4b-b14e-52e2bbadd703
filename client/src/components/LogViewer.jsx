import React, { useState, useEffect, useRef } from 'react'

const LogViewer = ({ logs = [] }) => {
  const [isAutoScroll, setIsAutoScroll] = useState(true)
  const [filter, setFilter] = useState('all')
  const logContainerRef = useRef(null)

  // Автоскролл к последнему сообщению
  useEffect(() => {
    if (isAutoScroll && logContainerRef.current) {
      logContainerRef.current.scrollTop = logContainerRef.current.scrollHeight
    }
  }, [logs, isAutoScroll])

  const filteredLogs = logs.filter(log => {
    if (filter === 'all') return true
    return log.level && log.level.toLowerCase() === filter
  })

  const getLogLevelColor = (level) => {
    if (!level) return '#333'
    switch (level.toLowerCase()) {
      case 'error': return '#dc3545'
      case 'warning': return '#ffc107'
      case 'info': return '#17a2b8'
      case 'debug': return '#6c757d'
      default: return '#333'
    }
  }

  const formatTimestamp = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString()
  }

  return (
    <div className="log-viewer">
      <div className="log-header">
        <h3>Логи системы</h3>
        <div className="log-controls">
          <select 
            value={filter} 
            onChange={(e) => setFilter(e.target.value)}
            className="log-filter"
          >
            <option value="all">Все уровни</option>
            <option value="error">Ошибки</option>
            <option value="warning">Предупреждения</option>
            <option value="info">Информация</option>
            <option value="debug">Отладка</option>
          </select>
          
          <label className="auto-scroll-toggle">
            <input
              type="checkbox"
              checked={isAutoScroll}
              onChange={(e) => setIsAutoScroll(e.target.checked)}
            />
            Автоскролл
          </label>
          
          <button 
            onClick={() => {
              if (logContainerRef.current) {
                logContainerRef.current.scrollTop = logContainerRef.current.scrollHeight
              }
            }}
            className="scroll-to-bottom"
          >
            ⬇ В конец
          </button>
        </div>
      </div>
      
      <div 
        ref={logContainerRef}
        className="log-container"
        onScroll={(e) => {
          const { scrollTop, scrollHeight, clientHeight } = e.target
          const isAtBottom = scrollTop + clientHeight >= scrollHeight - 5
          setIsAutoScroll(isAtBottom)
        }}
      >
        {filteredLogs.length === 0 ? (
          <div className="no-logs">Логи отсутствуют</div>
        ) : (
          filteredLogs.map((log, index) => (
            <div key={index} className="log-entry">
              <span className="log-timestamp">
                {formatTimestamp(log.timestamp)}
              </span>
              <span
                className="log-level"
                style={{ color: getLogLevelColor(log.level) }}
              >
                [{log.level ? log.level.toUpperCase() : 'UNKNOWN'}]
              </span>
              <span className="log-message">{log.message}</span>
              {log.chat_id && (
                <span className="log-chat">Chat: {log.chat_id}</span>
              )}
            </div>
          ))
        )}
      </div>
    </div>
  )
}

export default LogViewer
