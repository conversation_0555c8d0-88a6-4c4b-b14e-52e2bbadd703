import React, { useState, useEffect } from 'react'
import axios from 'axios'
import ChatList from './components/ChatList'
import Stats from './components/Stats'
import LogViewer from './components/LogViewer'
import DemoControls from './components/DemoControls'

function App() {
  const [chats, setChats] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [stats, setStats] = useState({
    totalChats: 0,
    activeChats: 0,
    messagesSent: 0
  })
  const [logs, setLogs] = useState([])

  // WebSocket для real-time обновлений
  useEffect(() => {
    const ws = new WebSocket('ws://localhost:8000/ws')

    ws.onmessage = (event) => {
      const data = JSON.parse(event.data)
      if (data.type === 'message_sent') {
        setStats(prev => ({
          ...prev,
          messagesSent: prev.messagesSent + 1
        }))

        // Добавляем лог о отправленном сообщении
        addLog('info', `Отправлено сообщение в чат ${data.chat_id}: ${data.message}`, data.chat_id)
      } else if (data.type === 'log') {
        addLog(data.level, data.message, data.chat_id)
      }
    }

    ws.onerror = (error) => {
      console.log('WebSocket error:', error)
      addLog('error', 'Ошибка подключения к серверу логов')
    }

    return () => ws.close()
  }, [])

  // Функция для добавления логов
  const addLog = (level, message, chatId = null) => {
    const newLog = {
      timestamp: new Date().toISOString(),
      level,
      message,
      chat_id: chatId
    }
    setLogs(prev => [...prev.slice(-99), newLog]) // Храним последние 100 логов
  }

  // Загрузка списка чатов
  const loadChats = async () => {
    try {
      setLoading(true)
      addLog('info', 'Загрузка списка чатов...')

      const response = await axios.get('/api/chats')
      // Безопасный сервер возвращает {chats: [...]}
      const chatsData = response.data.chats || response.data || []
      setChats(chatsData)

      // Обновляем статистику
      const totalChats = chatsData.length
      const activeChats = chatsData.filter(chat => chat.is_active === true).length
      setStats(prev => ({
        ...prev,
        totalChats,
        activeChats
      }))

      addLog('info', `Загружено ${totalChats} чатов, активных: ${activeChats}`)
      setError(null)
    } catch (err) {
      const errorMsg = 'Ошибка загрузки чатов: ' + err.message
      setError(errorMsg)
      addLog('error', errorMsg)
    } finally {
      setLoading(false)
    }
  }

  // Переключение автоответчика для чата
  const toggleChat = async (chatId, currentStatus) => {
    try {
      const action = currentStatus ? 'выключен' : 'включен'
      addLog('info', `Переключение автоответчика для чата ${chatId}...`, chatId)

      await axios.post(`/api/chats/${chatId}/toggle?active=${!currentStatus}`)

      // Обновляем локальное состояние
      setChats(prev => prev.map(chat =>
        chat.id === chatId
          ? { ...chat, active: !currentStatus, is_active: !currentStatus }
          : chat
      ))

      // Обновляем статистику
      setStats(prev => ({
        ...prev,
        activeChats: prev.activeChats + (currentStatus ? -1 : 1)
      }))

      addLog('info', `Автоответчик для чата ${chatId} ${action}`, chatId)

    } catch (err) {
      const errorMsg = 'Ошибка переключения автоответчика: ' + err.message
      setError(errorMsg)
      addLog('error', errorMsg, chatId)
    }
  }

  useEffect(() => {
    addLog('info', 'Приложение запущено')
    loadChats()
  }, [])

  return (
    <div className="container">
      <div className="header">
        <h1>Telegram Auto-Responder</h1>
        <p>Управление автоматическими ответами в Telegram чатах</p>
      </div>

      {error && (
        <div className="error">
          {error}
        </div>
      )}

      <Stats stats={stats} />

      <ChatList
        chats={chats}
        loading={loading}
        onToggleChat={toggleChat}
        onRefresh={loadChats}
      />

      <DemoControls
        chats={chats}
        onLogMessage={addLog}
      />

      <LogViewer logs={logs} />
    </div>
  )
}

export default App
