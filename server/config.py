# Конфигурация Telegram
TELEGRAM_CONFIG = {
    'api_id': 27481954,
    'api_hash': 'a796c4a2c24a16c8f535c4730b0a038b',
    'phone': '+79260444736',
    'session_name': 'autoresponder_session'
}

# Конфигурация OpenRouter API
OPENROUTER_CONFIG = {
    'api_key': '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',
    'model': 'deepseek/deepseek-chat'
}

# Настройки автоответчика
AUTORESPONDER_CONFIG = {
    'response_delay': 2,  # Задержка перед ответом в секундах
    'max_message_length': 1000,  # Максимальная длина ответа
    'default_welcome_message': "👋 Привет! Это автоматический ответчик. Напишите мне что-нибудь, и я отвечу с помощью ИИ!",
    'default_system_prompt': '''Ты помощник, который отвечает от имени пользователя в Telegram.
Отвечай кратко, дружелюбно и естественно.
Не раскрывай техническую информацию о том, как ты работаешь.'''
}

# Настройки сервера
SERVER_CONFIG = {
    'host': '0.0.0.0',
    'port': 8000,
    'debug': True
}
