# Telegram Auto-Responder

Приложение для автоматических ответов в Telegram чатах с использованием LLM модели и веб-интерфейсом для управления.

## Возможности

- 🤖 Автоматические ответы от имени вашего аккаунта Telegram
- 🎯 Выборочная активация для конкретных чатов
- 🧠 Интеграция с LLM моделями для генерации ответов
- 💬 Изолированный контекст для каждого чата
- 🔄 Периодический сброс контекста
- 📊 Веб-интерфейс с мониторингом статуса
- ⚡ Real-time обновления через WebSocket

## Технологии

- **Backend**: Python + FastAPI + Telethon
- **Frontend**: React + Vite
- **База данных**: SQLite
- **LLM**: Поддержка различных API (OpenAI, Anthropic, локальные модели)

## Установка и настройка

### 1. Клонирование и установка зависимостей

```bash
# Установка зависимостей для всего проекта
npm run install:all

# Или отдельно:
cd server && pip install -r requirements.txt
cd ../client && npm install
```

### 2. Настройка Telegram API

1. Перейдите на https://my.telegram.org/apps
2. Создайте новое приложение
3. Получите `api_id` и `api_hash`

### 3. Настройка переменных окружения

```bash
cd server
cp .env.example .env
```

Отредактируйте файл `.env`:

```env
# Telegram API credentials
TELEGRAM_API_ID=your_api_id
TELEGRAM_API_HASH=your_api_hash
TELEGRAM_PHONE=+1234567890

# LLM API settings
LLM_API_KEY=your_llm_api_key
LLM_MODEL=gpt-3.5-turbo
```

### 4. Первый запуск

```bash
# Запуск backend (в первый раз потребуется авторизация в Telegram)
cd server
python main.py

# В другом терминале - запуск frontend
cd client
npm run dev
```

При первом запуске Telethon попросит ввести код подтверждения из Telegram.

### 5. Использование

1. Откройте http://localhost:3000
2. Выберите чаты для активации автоответчика
3. Включите автоответчик для нужных чатов
4. Мониторьте статус и статистику в веб-интерфейсе

## Структура проекта

```
avtootvetchik/
├── server/                 # Python backend
│   ├── main.py            # Основной файл приложения
│   ├── requirements.txt   # Python зависимости
│   ├── .env.example      # Пример конфигурации
│   └── autoresponder.db  # SQLite база данных (создается автоматически)
├── client/                # React frontend
│   ├── src/
│   │   ├── components/   # React компоненты
│   │   ├── App.jsx      # Главный компонент
│   │   └── main.jsx     # Точка входа
│   ├── package.json
│   └── vite.config.js
└── package.json          # Корневой package.json
```

## API Endpoints

- `GET /api/chats` - Получение списка доступных чатов
- `POST /api/chats/{chat_id}/toggle?active=true/false` - Включение/выключение автоответчика
- `WebSocket /ws` - Real-time уведомления

## Безопасность

⚠️ **Важно**: 
- Никогда не делитесь своими API ключами
- Файл `.env` добавлен в `.gitignore`
- Используйте приложение ответственно
- Соблюдайте правила Telegram

## Разработка

```bash
# Запуск в режиме разработки
npm run dev

# Сборка для продакшена
npm run build
```

## Лицензия

MIT License
