import React, { useState, useMemo } from 'react'

const ChatList = ({ chats, loading, onToggleChat, onRefresh }) => {
  const [searchQuery, setSearchQuery] = useState('')
  const [isCollapsed, setIsCollapsed] = useState(false)

  // Фильтрация чатов по поисковому запросу
  const filteredChats = useMemo(() => {
    if (!searchQuery.trim()) return chats

    const query = searchQuery.toLowerCase()
    return chats.filter(chat => {
      const name = (chat.name || chat.title || '').toLowerCase()
      const type = (chat.type || '').toLowerCase()
      return name.includes(query) || type.includes(query)
    })
  }, [chats, searchQuery])

  if (loading) {
    return (
      <div className="chat-list">
        <div className="loading">Загрузка чатов...</div>
      </div>
    )
  }

  if (!chats || chats.length === 0) {
    return (
      <div className="chat-list">
        <div className="loading">
          Чаты не найдены.
          <button onClick={onRefresh} style={{ marginLeft: '10px' }}>
            Обновить
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="chat-list">
      <div style={{ padding: '15px 20px', borderBottom: '1px solid #eee', background: '#f8f9fa' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '10px' }}>
          <h3 style={{ margin: 0 }}>
            Доступные чаты ({filteredChats.length}/{chats.length})
          </h3>
          <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
            <button
              onClick={() => setIsCollapsed(!isCollapsed)}
              style={{
                fontSize: '12px',
                padding: '5px 10px',
                background: 'none',
                border: '1px solid #ddd',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              {isCollapsed ? '📋 Развернуть' : '📁 Свернуть'}
            </button>
            <button
              onClick={onRefresh}
              style={{
                fontSize: '12px',
                padding: '5px 10px',
                background: 'none',
                border: '1px solid #ddd',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              🔄 Обновить
            </button>
          </div>
        </div>

        {!isCollapsed && (
          <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
            <input
              type="text"
              placeholder="🔍 Поиск по чатам..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              style={{
                flex: 1,
                padding: '8px 12px',
                border: '1px solid #ddd',
                borderRadius: '4px',
                fontSize: '14px'
              }}
            />
            {searchQuery && (
              <button
                onClick={() => setSearchQuery('')}
                style={{
                  padding: '8px 12px',
                  background: '#f0f0f0',
                  border: '1px solid #ddd',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  fontSize: '12px'
                }}
              >
                ✕ Очистить
              </button>
            )}
          </div>
        )}
      </div>

      {!isCollapsed && (
        <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
          {filteredChats.length === 0 ? (
            <div style={{ padding: '20px', textAlign: 'center', color: '#666' }}>
              {searchQuery ? 'Чаты не найдены по запросу' : 'Нет доступных чатов'}
            </div>
          ) : (
            filteredChats.map(chat => {
              const isActive = chat.active || chat.is_active;
              return (
                <div key={chat.id} className="chat-item">
                  <div className="chat-info">
                    <div className="chat-title">{chat.name || chat.title}</div>
                    <div className="chat-type">{chat.type}</div>
                  </div>

                  <div className="chat-controls">
                    <div
                      className={`status-indicator ${isActive ? 'active' : ''}`}
                      title={isActive ? 'Активен' : 'Неактивен'}
                    />

                    <button
                      className={`toggle-button ${isActive ? 'active' : 'inactive'}`}
                      onClick={() => onToggleChat(chat.id, isActive)}
                    >
                      {isActive ? 'Выключить' : 'Включить'}
                    </button>
                  </div>
                </div>
              )
            })
          )}
        </div>
      )}
    </div>
  )
}

export default ChatList
