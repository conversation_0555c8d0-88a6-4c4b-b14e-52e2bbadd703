"""
LLM провайдеры для генерации ответов
"""

import os
import asyncio
import aiohttp
import json
from typing import List, Dict, Optional
from abc import ABC, abstractmethod

class LLMProvider(ABC):
    """Базовый класс для LLM провайдеров"""
    
    @abstractmethod
    async def generate_response(self, messages: List[Dict], context: str = "") -> Optional[str]:
        """Генерация ответа на основе сообщений и контекста"""
        pass

class OpenAIProvider(LLMProvider):
    """Провайдер для OpenAI API"""
    
    def __init__(self, api_key: str, model: str = "gpt-3.5-turbo"):
        self.api_key = api_key
        self.model = model
        self.base_url = "https://api.openai.com/v1/chat/completions"
    
    async def generate_response(self, messages: List[Dict], context: str = "") -> Optional[str]:
        try:
            # Формируем системный промпт
            system_prompt = f"""
Ты дружелюбный помощник, который отвечает в Telegram чате от имени пользователя.

Контекст разговора:
{context}

Правила:
1. Отвечай естественно и по теме разговора
2. Будь краток и дружелюбен
3. Используй неформальный стиль общения
4. Не упоминай, что ты ИИ или бот
5. Отвечай на русском языке, если сообщение на русском
6. Адаптируйся к стилю собеседника
"""

            # Подготавливаем сообщения для API
            api_messages = [
                {"role": "system", "content": system_prompt}
            ]
            
            # Добавляем последние сообщения
            for msg in messages[-5:]:  # Последние 5 сообщений
                role = "assistant" if msg['is_outgoing'] else "user"
                api_messages.append({
                    "role": role,
                    "content": msg['message']
                })
            
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "model": self.model,
                "messages": api_messages,
                "max_tokens": 150,
                "temperature": 0.7
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(self.base_url, headers=headers, json=payload) as response:
                    if response.status == 200:
                        data = await response.json()
                        return data['choices'][0]['message']['content'].strip()
                    else:
                        print(f"OpenAI API error: {response.status}")
                        return None
                        
        except Exception as e:
            print(f"Ошибка OpenAI API: {e}")
            return None

class AnthropicProvider(LLMProvider):
    """Провайдер для Anthropic Claude API"""
    
    def __init__(self, api_key: str, model: str = "claude-3-sonnet-20240229"):
        self.api_key = api_key
        self.model = model
        self.base_url = "https://api.anthropic.com/v1/messages"
    
    async def generate_response(self, messages: List[Dict], context: str = "") -> Optional[str]:
        try:
            # Формируем контекст для Claude
            context_text = f"Контекст разговора:\n{context}\n\n"
            
            # Последнее сообщение
            last_message = messages[-1]['message'] if messages else ""
            
            prompt = f"""
{context_text}
Ответь на последнее сообщение естественно и дружелюбно. Будь краток.
Последнее сообщение: {last_message}
"""

            headers = {
                "x-api-key": self.api_key,
                "Content-Type": "application/json",
                "anthropic-version": "2023-06-01"
            }
            
            payload = {
                "model": self.model,
                "max_tokens": 150,
                "messages": [
                    {"role": "user", "content": prompt}
                ]
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(self.base_url, headers=headers, json=payload) as response:
                    if response.status == 200:
                        data = await response.json()
                        return data['content'][0]['text'].strip()
                    else:
                        print(f"Anthropic API error: {response.status}")
                        return None
                        
        except Exception as e:
            print(f"Ошибка Anthropic API: {e}")
            return None

class LocalLLMProvider(LLMProvider):
    """Провайдер для локальных LLM моделей (например, через Ollama)"""
    
    def __init__(self, base_url: str = "http://localhost:11434", model: str = "llama2"):
        self.base_url = base_url
        self.model = model
    
    async def generate_response(self, messages: List[Dict], context: str = "") -> Optional[str]:
        try:
            # Формируем промпт для локальной модели
            context_text = f"Контекст: {context}\n\n" if context else ""
            
            conversation = ""
            for msg in messages[-5:]:
                role = "Я" if msg['is_outgoing'] else "Собеседник"
                conversation += f"{role}: {msg['message']}\n"
            
            prompt = f"""
{context_text}{conversation}

Ответь на последнее сообщение собеседника естественно и дружелюбно. Будь краток.
Я:"""

            payload = {
                "model": self.model,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": 0.7,
                    "max_tokens": 150
                }
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(f"{self.base_url}/api/generate", json=payload) as response:
                    if response.status == 200:
                        data = await response.json()
                        return data['response'].strip()
                    else:
                        print(f"Local LLM API error: {response.status}")
                        return None
                        
        except Exception as e:
            print(f"Ошибка Local LLM API: {e}")
            return None

class OpenRouterProvider(LLMProvider):
    """OpenRouter API провайдер (поддерживает множество моделей включая бесплатный DeepSeek)"""

    def __init__(self, api_key: str, model: str = "deepseek/deepseek-chat"):
        self.api_key = api_key
        self.model = model
        self.base_url = "https://openrouter.ai/api/v1"

    async def generate_response(self, messages: List[Dict], context: str = "") -> Optional[str]:
        try:
            # Формируем контекст из истории сообщений
            conversation = ""
            for msg in messages[-5:]:  # Последние 5 сообщений
                role = "Я" if msg['is_outgoing'] else "Собеседник"
                conversation += f"{role}: {msg['message']}\n"

            context_text = f"Контекст чата: {context}\n\n" if context else ""

            # Формируем сообщения для OpenRouter
            chat_messages = [
                {"role": "system", "content": "Ты дружелюбный помощник в Telegram чате. Отвечай кратко и естественно на русском языке. Будь вежливым и полезным."}
            ]

            # Добавляем контекст если есть
            if context:
                chat_messages.append({"role": "system", "content": f"Контекст чата: {context}"})

            # Добавляем историю разговора
            for msg in messages[-8:]:  # Последние 8 сообщений для контекста
                role = "assistant" if msg['is_outgoing'] else "user"
                chat_messages.append({"role": role, "content": msg['message']})

            payload = {
                "model": self.model,
                "messages": chat_messages,
                "max_tokens": 200,
                "temperature": 0.7,
                "stream": False
            }

            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json",
                "HTTP-Referer": "http://localhost:3000",  # Для OpenRouter
                "X-Title": "Telegram Auto-Responder"  # Для OpenRouter
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(f"{self.base_url}/chat/completions", headers=headers, json=payload) as response:
                    if response.status == 200:
                        data = await response.json()
                        return data['choices'][0]['message']['content'].strip()
                    else:
                        error_text = await response.text()
                        print(f"OpenRouter API error: {response.status} - {error_text}")
                        return None

        except Exception as e:
            print(f"Ошибка OpenRouter API: {e}")
            return None

class MockProvider(LLMProvider):
    """Заглушка для тестирования"""

    async def generate_response(self, messages: List[Dict], context: str = "") -> Optional[str]:
        if not messages:
            return "Привет! Как дела?"

        last_message = messages[-1]['message'].lower()

        # Простые ответы на основе ключевых слов
        if any(word in last_message for word in ['привет', 'hello', 'hi']):
            return "Привет! Как дела?"
        elif any(word in last_message for word in ['как дела', 'как ты', 'how are you']):
            return "Всё отлично, спасибо! А у тебя как?"
        elif any(word in last_message for word in ['пока', 'bye', 'goodbye']):
            return "Пока! Удачи!"
        elif '?' in last_message:
            return "Интересный вопрос! Дай подумать..."
        else:
            return f"Понял тебя! Ты написал: '{messages[-1]['message'][:50]}...'"

def get_llm_provider() -> LLMProvider:
    """Фабрика для создания LLM провайдера на основе настроек"""
    
    provider_type = os.getenv('LLM_PROVIDER', 'mock').lower()
    
    if provider_type == 'openai':
        api_key = os.getenv('OPENAI_API_KEY')
        model = os.getenv('OPENAI_MODEL', 'gpt-3.5-turbo')
        if not api_key:
            print("OPENAI_API_KEY не найден, используется заглушка")
            return MockProvider()
        return OpenAIProvider(api_key, model)
    
    elif provider_type == 'anthropic':
        api_key = os.getenv('ANTHROPIC_API_KEY')
        model = os.getenv('ANTHROPIC_MODEL', 'claude-3-sonnet-20240229')
        if not api_key:
            print("ANTHROPIC_API_KEY не найден, используется заглушка")
            return MockProvider()
        return AnthropicProvider(api_key, model)
    
    elif provider_type == 'local':
        base_url = os.getenv('LOCAL_LLM_URL', 'http://localhost:11434')
        model = os.getenv('LOCAL_LLM_MODEL', 'llama2')
        return LocalLLMProvider(base_url, model)

    elif provider_type == 'openrouter':
        api_key = os.getenv('OPENROUTER_API_KEY')
        model = os.getenv('OPENROUTER_MODEL', 'deepseek/deepseek-chat')
        if not api_key:
            print("OPENROUTER_API_KEY не найден, используется заглушка")
            return MockProvider()
        return OpenRouterProvider(api_key, model)

    else:
        print(f"Используется заглушка LLM провайдера (provider: {provider_type})")
        return MockProvider()
