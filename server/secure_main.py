#!/usr/bin/env python3
"""
Безопасный Telegram автоответчик
Принципы безопасности:
1. Никакого сохранения истории сообщений
2. Только выбранные чаты
3. LLM видит только последнее сообщение
4. Прозрачность - всегда указывать что это автоответчик
5. Подробное логирование всех действий
"""

import asyncio
import logging
import os
import json
from datetime import datetime
from typing import Dict, Set
from dotenv import load_dotenv
from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn

from secure_llm_providers import get_secure_llm_provider

# Загружаем переменные окружения
load_dotenv()

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SecureTelegramAutoResponder:
    """Безопасный Telegram автоответчик"""
    
    def __init__(self):
        self.active_chats: Set[int] = set()  # Активные чаты (только ID)
        self.llm_provider = get_secure_llm_provider()
        self.websocket_connections: Set[WebSocket] = set()
        
        # Демо-режим для тестирования
        self.demo_mode = True
        self.demo_chats = {
            1: {"name": "Чат с Алексом", "type": "private"},
            2: {"name": "Чат с Марией", "type": "private"},
            3: {"name": "Рабочая группа", "type": "group"}
        }
        
        logger.info("🔒 Безопасный автоответчик инициализирован")
        logger.info(f"🔒 Провайдер LLM: {type(self.llm_provider).__name__}")
    
    async def log_action(self, action: str, chat_id: int = None, details: str = ""):
        """Безопасное логирование действий"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        chat_info = f"[Чат {chat_id}]" if chat_id else ""
        log_message = f"🔒 {timestamp} {chat_info} {action}"
        if details:
            log_message += f" - {details}"
        
        logger.info(log_message)
        
        # Отправляем в WebSocket для реального времени
        await self.broadcast_log(log_message)
    
    async def broadcast_log(self, message: str):
        """Отправка логов в WebSocket"""
        if self.websocket_connections:
            disconnected = set()
            for websocket in self.websocket_connections:
                try:
                    await websocket.send_text(json.dumps({
                        "type": "log",
                        "message": message,
                        "timestamp": datetime.now().isoformat()
                    }))
                except:
                    disconnected.add(websocket)
            
            # Удаляем отключенные соединения
            self.websocket_connections -= disconnected
    
    async def toggle_chat(self, chat_id: int, active: bool) -> bool:
        """Безопасное включение/выключение автоответчика для чата"""
        try:
            if active:
                self.active_chats.add(chat_id)
                await self.log_action("Автоответчик включен", chat_id)
            else:
                self.active_chats.discard(chat_id)
                await self.log_action("Автоответчик выключен", chat_id)
            
            return True
        except Exception as e:
            await self.log_action("Ошибка переключения автоответчика", chat_id, str(e))
            return False
    
    async def process_message(self, chat_id: int, message: str, sender_name: str = "Собеседник") -> str:
        """
        Безопасная обработка сообщения
        Никакого сохранения истории - только обработка текущего сообщения
        """
        try:
            # Проверяем что автоответчик включен для этого чата
            if chat_id not in self.active_chats:
                await self.log_action("Сообщение проигнорировано - автоответчик выключен", chat_id)
                return None
            
            await self.log_action("Получено сообщение", chat_id, f"от {sender_name}: '{message[:50]}...'")
            
            # Определяем минимальный контекст чата
            chat_context = ""
            if chat_id in self.demo_chats:
                chat_info = self.demo_chats[chat_id]
                if chat_info["type"] == "private":
                    chat_context = "личный чат"
                elif chat_info["type"] == "group":
                    chat_context = "групповой чат"
            
            # Генерируем ответ через LLM (только последнее сообщение!)
            await self.log_action("Отправка в LLM", chat_id, f"сообщение: '{message[:30]}...'")
            
            response = await self.llm_provider.generate_response(message, chat_context)
            
            if response:
                await self.log_action("Получен ответ от LLM", chat_id, f"ответ: '{response[:50]}...'")
                await self.log_action("Отправлен ответ", chat_id, f"'{response[:50]}...'")
                return response
            else:
                await self.log_action("Ошибка генерации ответа", chat_id)
                return "🤖 Автоответчик: Извините, произошла ошибка при генерации ответа."
                
        except Exception as e:
            await self.log_action("Критическая ошибка обработки", chat_id, str(e))
            return "🤖 Автоответчик: Произошла техническая ошибка."
    
    def get_demo_chats(self):
        """Получение списка демо-чатов"""
        chats = []
        for chat_id, info in self.demo_chats.items():
            chats.append({
                "id": chat_id,
                "name": info["name"],
                "type": info["type"],
                "active": chat_id in self.active_chats
            })
        return chats

# Создаем экземпляр приложения
app = FastAPI(title="Secure Telegram Auto-Responder")
responder = SecureTelegramAutoResponder()

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket для реального времени логов"""
    await websocket.accept()
    responder.websocket_connections.add(websocket)
    await responder.log_action("WebSocket подключение установлено")
    
    try:
        while True:
            await websocket.receive_text()
    except WebSocketDisconnect:
        responder.websocket_connections.discard(websocket)
        await responder.log_action("WebSocket подключение закрыто")

@app.get("/api/chats")
async def get_chats():
    """Получение списка чатов"""
    return {"chats": responder.get_demo_chats()}

@app.post("/api/chats/{chat_id}/toggle")
async def toggle_chat(chat_id: int, active: bool):
    """Включение/выключение автоответчика для чата"""
    success = await responder.toggle_chat(chat_id, active)
    return {"success": success}

@app.post("/api/demo/simulate_message")
async def simulate_message(chat_id: int, message: str):
    """Демо-симуляция входящего сообщения"""
    await responder.log_action("ДЕМО: Симуляция сообщения", chat_id, f"'{message}'")
    
    response = await responder.process_message(chat_id, message, "Демо-пользователь")
    
    return {
        "success": True,
        "response": response,
        "chat_id": chat_id,
        "original_message": message
    }

@app.on_event("startup")
async def startup_event():
    await responder.log_action("🔒 Безопасный сервер запущен")
    await responder.log_action("🔒 Демо-режим активен - реальный Telegram не подключен")

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
