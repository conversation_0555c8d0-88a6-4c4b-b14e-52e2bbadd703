#!/usr/bin/env python3
"""
Telegram Auto-Responder с использованием Telethon и LLM
"""

import asyncio
import os
import json
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import logging

from telethon import TelegramClient, events
from telethon.tl.types import User, Chat, Channel
from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import uvicorn
from dotenv import load_dotenv
from llm_providers import get_llm_provider

# Загружаем переменные окружения
load_dotenv()

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TelegramAutoResponder:
    def __init__(self):
        # Telegram API credentials
        self.api_id = os.getenv('TELEGRAM_API_ID')
        self.api_hash = os.getenv('TELEGRAM_API_HASH')
        self.phone = os.getenv('TELEGRAM_PHONE')

        # Проверяем, настроены ли реальные ключи
        self.demo_mode = (
            not self.api_id or
            not self.api_hash or
            self.api_id == 'your_api_id' or
            self.api_hash == 'your_api_hash'
        )

        # LLM провайдер
        self.llm_provider = get_llm_provider()

        # Инициализация клиента Telegram (только если не демо режим)
        if not self.demo_mode:
            self.client = TelegramClient('session', self.api_id, self.api_hash)
        else:
            self.client = None
            logger.info("Запуск в демо-режиме (Telegram API не настроен)")
        
        # Активные чаты для автоответов
        self.active_chats: Dict[int, bool] = {}
        
        # Контекст для каждого чата
        self.chat_contexts: Dict[int, List[Dict]] = {}
        
        # Максимальный размер контекста
        self.max_context_size = 20
        
        # WebSocket соединения для уведомлений
        self.websocket_connections: List[WebSocket] = []
        
        # Инициализация базы данных
        self.init_database()
    
    def init_database(self):
        """Инициализация SQLite базы данных"""
        conn = sqlite3.connect('autoresponder.db')
        cursor = conn.cursor()
        
        # Таблица для настроек чатов
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS chat_settings (
                chat_id INTEGER PRIMARY KEY,
                chat_title TEXT,
                is_active BOOLEAN DEFAULT FALSE,
                context_reset_interval INTEGER DEFAULT 3600,
                last_context_reset TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Таблица для истории сообщений
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS message_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                chat_id INTEGER,
                message_text TEXT,
                is_outgoing BOOLEAN,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (chat_id) REFERENCES chat_settings (chat_id)
            )
        ''')
        
        conn.commit()
        conn.close()
    
    async def start_client(self):
        """Запуск Telegram клиента"""
        if self.demo_mode:
            logger.info("Демо-режим: Telegram клиент не запускается")
            return

        await self.client.start(phone=self.phone)
        logger.info("Telegram клиент запущен")

        # Регистрация обработчика новых сообщений
        @self.client.on(events.NewMessage)
        async def handle_new_message(event):
            await self.process_new_message(event)
    
    async def process_new_message(self, event):
        """Обработка нового сообщения"""
        chat_id = event.chat_id
        message_text = event.message.message
        
        # Проверяем, активен ли автоответчик для этого чата
        if chat_id not in self.active_chats or not self.active_chats[chat_id]:
            return
        
        # Игнорируем собственные сообщения
        if event.message.out:
            return
        
        logger.info(f"Получено сообщение в чате {chat_id}: {message_text}")
        
        # Сохраняем сообщение в контекст
        await self.add_to_context(chat_id, message_text, is_outgoing=False)
        
        # Генерируем ответ
        response = await self.generate_response(chat_id, message_text)
        
        if response:
            # Отправляем ответ
            await self.client.send_message(chat_id, response)
            
            # Сохраняем ответ в контекст
            await self.add_to_context(chat_id, response, is_outgoing=True)
            
            # Уведомляем веб-интерфейс
            await self.notify_websockets({
                'type': 'message_sent',
                'chat_id': chat_id,
                'message': response
            })
    
    async def add_to_context(self, chat_id: int, message: str, is_outgoing: bool):
        """Добавление сообщения в контекст чата"""
        if chat_id not in self.chat_contexts:
            self.chat_contexts[chat_id] = []
        
        context_entry = {
            'message': message,
            'is_outgoing': is_outgoing,
            'timestamp': datetime.now().isoformat()
        }
        
        self.chat_contexts[chat_id].append(context_entry)
        
        # Ограничиваем размер контекста
        if len(self.chat_contexts[chat_id]) > self.max_context_size:
            self.chat_contexts[chat_id] = self.chat_contexts[chat_id][-self.max_context_size:]
        
        # Сохраняем в базу данных
        conn = sqlite3.connect('autoresponder.db')
        cursor = conn.cursor()
        cursor.execute(
            'INSERT INTO message_history (chat_id, message_text, is_outgoing) VALUES (?, ?, ?)',
            (chat_id, message, is_outgoing)
        )
        conn.commit()
        conn.close()
    
    async def generate_response(self, chat_id: int, message: str) -> Optional[str]:
        """Генерация ответа с помощью LLM"""
        try:
            # Получаем контекст чата
            context = self.chat_contexts.get(chat_id, [])

            # Формируем контекст для LLM
            context_text = ""
            for entry in context[-10:]:  # Последние 10 сообщений
                role = "Я" if entry['is_outgoing'] else "Собеседник"
                context_text += f"{role}: {entry['message']}\n"

            # Генерируем ответ через LLM провайдер
            response = await self.llm_provider.generate_response(context, context_text)

            return response

        except Exception as e:
            logger.error(f"Ошибка генерации ответа: {e}")
            return None
    
    async def get_chats(self) -> List[Dict]:
        """Получение списка доступных чатов"""
        if self.demo_mode:
            # Возвращаем демо-чаты
            return [
                {
                    'id': 1,
                    'title': 'Демо чат 1',
                    'type': 'User',
                    'is_active': self.active_chats.get(1, False)
                },
                {
                    'id': 2,
                    'title': 'Демо группа',
                    'type': 'Chat',
                    'is_active': self.active_chats.get(2, False)
                },
                {
                    'id': 3,
                    'title': 'Демо канал',
                    'type': 'Channel',
                    'is_active': self.active_chats.get(3, False)
                }
            ]

        chats = []
        async for dialog in self.client.iter_dialogs():
            if isinstance(dialog.entity, (User, Chat, Channel)):
                chats.append({
                    'id': dialog.entity.id,
                    'title': dialog.title,
                    'type': type(dialog.entity).__name__,
                    'is_active': self.active_chats.get(dialog.entity.id, False)
                })
        return chats
    
    async def toggle_chat_autoresponder(self, chat_id: int, active: bool):
        """Включение/выключение автоответчика для чата"""
        self.active_chats[chat_id] = active
        
        # Сохраняем в базу данных
        conn = sqlite3.connect('autoresponder.db')
        cursor = conn.cursor()
        cursor.execute(
            'INSERT OR REPLACE INTO chat_settings (chat_id, is_active) VALUES (?, ?)',
            (chat_id, active)
        )
        conn.commit()
        conn.close()
        
        logger.info(f"Автоответчик для чата {chat_id}: {'включен' if active else 'выключен'}")
    
    async def notify_websockets(self, data: Dict):
        """Отправка уведомлений через WebSocket"""
        if self.websocket_connections:
            message = json.dumps(data)
            for websocket in self.websocket_connections[:]:
                try:
                    await websocket.send_text(message)
                except:
                    self.websocket_connections.remove(websocket)

# Глобальный экземпляр автоответчика
auto_responder = TelegramAutoResponder()

# FastAPI приложение
app = FastAPI(title="Telegram Auto-Responder API")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.on_event("startup")
async def startup_event():
    """Запуск при старте приложения"""
    await auto_responder.start_client()

@app.get("/api/chats")
async def get_chats():
    """Получение списка чатов"""
    return await auto_responder.get_chats()

@app.post("/api/chats/{chat_id}/toggle")
async def toggle_chat(chat_id: int, active: bool):
    """Включение/выключение автоответчика для чата"""
    await auto_responder.toggle_chat_autoresponder(chat_id, active)
    return {"success": True}

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket для real-time уведомлений"""
    await websocket.accept()
    auto_responder.websocket_connections.append(websocket)
    
    try:
        while True:
            await websocket.receive_text()
    except WebSocketDisconnect:
        auto_responder.websocket_connections.remove(websocket)

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
