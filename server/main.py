#!/usr/bin/env python3
"""
Telegram Auto-Responder с использованием Telethon и LLM
"""

import asyncio
import os
import json
import sqlite3
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional
import logging

from telethon import TelegramClient, events
from telethon.tl.types import User, Chat, Channel
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import uvicorn
from dotenv import load_dotenv
from llm_providers import get_llm_provider
from config import TELEGRAM_CONFIG, OPENROUTER_CONFIG, AUTORESPONDER_CONFIG, SERVER_CONFIG

# Загружаем переменные окружения
load_dotenv()

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TelegramAutoResponder:
    def __init__(self):
        # Telegram API credentials из config.py
        self.api_id = TELEGRAM_CONFIG['api_id']
        self.api_hash = TELEGRAM_CONFIG['api_hash']
        self.phone = TELEGRAM_CONFIG['phone']
        self.session_name = TELEGRAM_CONFIG['session_name']

        # Проверяем, настроены ли реальные ключи
        self.demo_mode = (
            not self.api_id or
            not self.api_hash or
            str(self.api_id) == 'your_api_id' or
            self.api_hash == 'your_api_hash'
        )

        # LLM провайдер
        self.llm_provider = get_llm_provider()

        # Инициализация клиента Telegram (только если не демо режим)
        if not self.demo_mode:
            self.client = TelegramClient(self.session_name, self.api_id, self.api_hash)
            logger.info(f"Инициализация Telegram клиента для номера {self.phone}")
        else:
            self.client = None
            logger.info("Запуск в демо-режиме (Telegram API не настроен)")
        
        # Активные чаты для автоответов
        self.active_chats: Dict[int, bool] = {}

        # Контекст для каждого чата
        self.chat_contexts: Dict[int, List[Dict]] = {}

        # Максимальный размер контекста
        self.max_context_size = 20

        # Настройки чатов (приветственные сообщения, промпты)
        self.chat_settings: Dict[int, Dict] = {}

        # Отслеживание первых сообщений в чатах
        self.first_message_sent: Dict[int, bool] = {}
        
        # WebSocket соединения для уведомлений
        self.websocket_connections: List[WebSocket] = []

        # Настройка логирования с отправкой в WebSocket
        self.setup_websocket_logging()
        
        # Инициализация базы данных
        self.init_database()

        # Загружаем состояние чатов из базы данных
        self.load_chat_states()

    def setup_websocket_logging(self):
        """Настройка отправки логов через WebSocket"""
        class WebSocketHandler(logging.Handler):
            def __init__(self, auto_responder):
                super().__init__()
                self.auto_responder = auto_responder

            def emit(self, record):
                try:
                    log_data = {
                        'type': 'log',
                        'level': record.levelname.lower(),
                        'message': record.getMessage(),
                        'timestamp': record.created * 1000,  # JavaScript timestamp
                        'chat_id': getattr(record, 'chat_id', None)
                    }
                    asyncio.create_task(self.auto_responder.notify_websockets(log_data))
                except:
                    pass

        # Добавляем WebSocket handler к логгеру
        websocket_handler = WebSocketHandler(self)
        websocket_handler.setLevel(logging.INFO)
        logger.addHandler(websocket_handler)
    
    def init_database(self):
        """Инициализация SQLite базы данных"""
        logger.info("🔧 Инициализация базы данных...")
        conn = sqlite3.connect('autoresponder.db')
        cursor = conn.cursor()
        
        # Таблица для настроек чатов
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS chat_settings (
                chat_id INTEGER PRIMARY KEY,
                chat_title TEXT,
                is_active BOOLEAN DEFAULT FALSE,
                context_reset_interval INTEGER DEFAULT 3600,
                last_context_reset TIMESTAMP,
                welcome_message TEXT,
                system_prompt TEXT,
                first_message_sent BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Таблица для истории сообщений
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS message_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                chat_id INTEGER,
                message_text TEXT,
                is_outgoing BOOLEAN,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (chat_id) REFERENCES chat_settings (chat_id)
            )
        ''')
        
        # Добавляем новые колонки если их нет
        try:
            cursor.execute('ALTER TABLE chat_settings ADD COLUMN welcome_message TEXT')
        except sqlite3.OperationalError:
            pass  # Колонка уже существует

        try:
            cursor.execute('ALTER TABLE chat_settings ADD COLUMN system_prompt TEXT')
        except sqlite3.OperationalError:
            pass  # Колонка уже существует

        try:
            cursor.execute('ALTER TABLE chat_settings ADD COLUMN first_message_sent BOOLEAN DEFAULT FALSE')
        except sqlite3.OperationalError:
            pass  # Колонка уже существует

        conn.commit()
        conn.close()
        logger.info("✅ База данных инициализирована успешно")

    def load_chat_states(self):
        """Загрузка состояния чатов из базы данных"""
        try:
            conn = sqlite3.connect('autoresponder.db')
            cursor = conn.cursor()
            cursor.execute('SELECT chat_id, is_active, first_message_sent FROM chat_settings')
            results = cursor.fetchall()
            conn.close()

            for chat_id, is_active, first_message_sent in results:
                self.active_chats[chat_id] = bool(is_active)
                self.first_message_sent[chat_id] = bool(first_message_sent)

            logger.info(f"Загружено состояние для {len(results)} чатов")
        except Exception as e:
            logger.error(f"Ошибка загрузки состояния чатов: {e}")

    async def start_client(self):
        """Запуск Telegram клиента"""
        if self.demo_mode:
            logger.info("Демо-режим: Telegram клиент не запускается")
            return

        await self.client.start(phone=self.phone)
        logger.info("Telegram клиент запущен")

        # Регистрация обработчика новых сообщений
        @self.client.on(events.NewMessage)
        async def handle_new_message(event):
            await self.process_new_message(event)
    
    async def process_new_message(self, event):
        """Обработка нового сообщения"""
        try:
            chat_id = event.chat_id
            message_text = event.message.message

            # Логируем ВСЕ входящие сообщения для отладки
            logger.info(f"🔍 ОТЛАДКА: Получено сообщение от чата {chat_id}: {message_text[:50]}{'...' if len(message_text) > 50 else ''}")
            logger.info(f"🔍 ОТЛАДКА: Исходящее сообщение: {event.message.out}")
            logger.info(f"🔍 ОТЛАДКА: Активные чаты: {list(self.active_chats.keys())}")
            logger.info(f"🔍 ОТЛАДКА: Состояние для чата {chat_id}: {self.active_chats.get(chat_id, False)}")

            # Игнорируем собственные сообщения
            if event.message.out:
                logger.info(f"🔍 ОТЛАДКА: Пропускаем собственное сообщение")
                return

            # Проверяем, активен ли автоответчик для этого чата
            if chat_id not in self.active_chats or not self.active_chats[chat_id]:
                logger.info(f"🔍 ОТЛАДКА: Автоответчик неактивен для чата {chat_id}")
                return

            # Логируем сообщения из активных чатов
            logger.info(f"✅ Обрабатываем сообщение от активного чата {chat_id}: {message_text[:50]}{'...' if len(message_text) > 50 else ''}")
        except Exception as e:
            logger.error(f"❌ Ошибка в начале process_new_message: {e}")
            return
        
        try:
            # Логируем входящее сообщение с привязкой к чату
            log_record = logger.makeRecord(
                logger.name, logging.INFO, __file__, 0,
                f"Получено сообщение: {message_text[:50]}{'...' if len(message_text) > 50 else ''}",
                (), None
            )
            log_record.chat_id = chat_id
            logger.handle(log_record)

            logger.info(f"🔄 Начинаем обработку сообщения для чата {chat_id}")

            # Сохраняем сообщение в контекст
            await self.add_to_context(chat_id, message_text, is_outgoing=False)
            logger.info(f"🔄 Сообщение сохранено в контекст для чата {chat_id}")

            # Проверяем, нужно ли отправить приветственное сообщение
            if not self.first_message_sent.get(chat_id, False):
                logger.info(f"🔄 Отправляем приветственное сообщение для чата {chat_id}")
                welcome_message = await self.get_welcome_message(chat_id)
                response = welcome_message
                self.first_message_sent[chat_id] = True

                # Обновляем в базе данных
                await self.update_first_message_sent(chat_id, True)
            else:
                # Генерируем ответ с помощью LLM
                logger.info(f"🔄 Генерируем ответ с помощью LLM для чата {chat_id}")
                response = await self.generate_response(chat_id, message_text)

            logger.info(f"🔄 Ответ подготовлен для чата {chat_id}: {response[:50] if response else 'None'}{'...' if response and len(response) > 50 else ''}")

            if response:
                # Отправляем ответ
                if not self.demo_mode:
                    logger.info(f"🔄 Отправляем ответ в Telegram для чата {chat_id}")
                    await self.client.send_message(chat_id, response)
                    logger.info(f"✅ Ответ отправлен в Telegram для чата {chat_id}")

                # Сохраняем ответ в контекст
                await self.add_to_context(chat_id, response, is_outgoing=True)

                # Уведомляем веб-интерфейс
                await self.notify_websockets({
                    'type': 'message_sent',
                    'chat_id': chat_id,
                    'message': response
                })

                # Логируем с привязкой к чату
                log_record = logger.makeRecord(
                    logger.name, logging.INFO, __file__, 0,
                    f"Отправлен ответ: {response[:50]}{'...' if len(response) > 50 else ''}",
                    (), None
                )
                log_record.chat_id = chat_id
                logger.handle(log_record)

                # Проверяем, что автоответчик все еще активен после отправки
                logger.info(f"🔍 После отправки ответа - состояние автоответчика для чата {chat_id}: {self.active_chats.get(chat_id, False)}")
            else:
                logger.warning(f"❌ Не удалось сгенерировать ответ для чата {chat_id}")

        except Exception as e:
            logger.error(f"❌ Ошибка при обработке сообщения для чата {chat_id}: {e}")
            import traceback
            logger.error(f"❌ Трассировка: {traceback.format_exc()}")

    async def get_welcome_message(self, chat_id: int) -> str:
        """Получение приветственного сообщения для чата"""
        try:
            conn = sqlite3.connect('autoresponder.db')
            cursor = conn.cursor()
            cursor.execute('SELECT welcome_message FROM chat_settings WHERE chat_id = ?', (chat_id,))
            result = cursor.fetchone()
            conn.close()

            if result and result[0]:
                return result[0]
            else:
                return AUTORESPONDER_CONFIG['default_welcome_message']
        except Exception as e:
            logger.error(f"Ошибка получения приветственного сообщения: {e}")
            return AUTORESPONDER_CONFIG['default_welcome_message']

    async def get_system_prompt(self, chat_id: int) -> str:
        """Получение системного промпта для чата"""
        try:
            conn = sqlite3.connect('autoresponder.db')
            cursor = conn.cursor()
            cursor.execute('SELECT system_prompt FROM chat_settings WHERE chat_id = ?', (chat_id,))
            result = cursor.fetchone()
            conn.close()

            if result and result[0]:
                return result[0]
            else:
                return AUTORESPONDER_CONFIG['default_system_prompt']
        except Exception as e:
            logger.error(f"Ошибка получения системного промпта: {e}")
            return AUTORESPONDER_CONFIG['default_system_prompt']

    async def update_first_message_sent(self, chat_id: int, sent: bool):
        """Обновление флага отправки первого сообщения"""
        try:
            conn = sqlite3.connect('autoresponder.db')
            cursor = conn.cursor()
            cursor.execute(
                'UPDATE chat_settings SET first_message_sent = ? WHERE chat_id = ?',
                (sent, chat_id)
            )
            conn.commit()
            conn.close()
        except Exception as e:
            logger.error(f"Ошибка обновления флага первого сообщения: {e}")
    
    async def add_to_context(self, chat_id: int, message: str, is_outgoing: bool):
        """Добавление сообщения в контекст чата"""
        if chat_id not in self.chat_contexts:
            self.chat_contexts[chat_id] = []
        
        context_entry = {
            'message': message,
            'is_outgoing': is_outgoing,
            'timestamp': datetime.now().isoformat()
        }
        
        self.chat_contexts[chat_id].append(context_entry)
        
        # Ограничиваем размер контекста
        if len(self.chat_contexts[chat_id]) > self.max_context_size:
            self.chat_contexts[chat_id] = self.chat_contexts[chat_id][-self.max_context_size:]
        
        # Сохраняем в базу данных
        conn = sqlite3.connect('autoresponder.db')
        cursor = conn.cursor()
        cursor.execute(
            'INSERT INTO message_history (chat_id, message_text, is_outgoing) VALUES (?, ?, ?)',
            (chat_id, message, is_outgoing)
        )
        conn.commit()
        conn.close()
    
    async def generate_response(self, chat_id: int, message: str) -> Optional[str]:
        """Генерация ответа с помощью LLM"""
        try:
            # Получаем контекст чата
            context = self.chat_contexts.get(chat_id, [])

            # Формируем контекст для LLM
            context_text = ""
            for entry in context[-10:]:  # Последние 10 сообщений
                role = "Я" if entry['is_outgoing'] else "Собеседник"
                context_text += f"{role}: {entry['message']}\n"

            # Получаем кастомный системный промпт для этого чата
            system_prompt = await self.get_system_prompt(chat_id)
            logger.info(f"🔄 Используем системный промпт для чата {chat_id}: {system_prompt[:100]}...")

            # Генерируем ответ через LLM провайдер с кастомным промптом
            response = await self.llm_provider.generate_response_with_prompt(message, context_text, system_prompt)

            return response

        except Exception as e:
            logger.error(f"Ошибка генерации ответа: {e}")
            return None
    
    async def get_chats(self) -> List[Dict]:
        """Получение списка доступных чатов"""
        if self.demo_mode:
            # Возвращаем демо-чаты
            return [
                {
                    'id': 1,
                    'title': 'Демо чат 1',
                    'type': 'User',
                    'is_active': self.active_chats.get(1, False)
                },
                {
                    'id': 2,
                    'title': 'Демо группа',
                    'type': 'Chat',
                    'is_active': self.active_chats.get(2, False)
                },
                {
                    'id': 3,
                    'title': 'Демо канал',
                    'type': 'Channel',
                    'is_active': self.active_chats.get(3, False)
                }
            ]

        chats = []
        async for dialog in self.client.iter_dialogs():
            if isinstance(dialog.entity, (User, Chat, Channel)):
                chats.append({
                    'id': dialog.entity.id,
                    'title': dialog.title,
                    'type': type(dialog.entity).__name__,
                    'is_active': self.active_chats.get(dialog.entity.id, False)
                })
        return chats
    
    async def toggle_chat_autoresponder(self, chat_id: int, active: bool):
        """Включение/выключение автоответчика для чата"""
        self.active_chats[chat_id] = active

        # При включении автоответчика сбрасываем флаг первого сообщения
        if active:
            self.first_message_sent[chat_id] = False

        # Сохраняем в базу данных
        conn = sqlite3.connect('autoresponder.db')
        cursor = conn.cursor()
        cursor.execute(
            'INSERT OR REPLACE INTO chat_settings (chat_id, is_active, first_message_sent) VALUES (?, ?, ?)',
            (chat_id, active, False if active else self.first_message_sent.get(chat_id, False))
        )
        conn.commit()
        conn.close()

        status = 'включен' if active else 'выключен'
        log_record = logger.makeRecord(
            logger.name, logging.INFO, __file__, 0,
            f"Автоответчик для чата {chat_id}: {status}",
            (), None
        )
        log_record.chat_id = chat_id
        logger.handle(log_record)
    
    async def notify_websockets(self, data: Dict):
        """Отправка уведомлений через WebSocket"""
        if self.websocket_connections:
            message = json.dumps(data)
            for websocket in self.websocket_connections[:]:
                try:
                    await websocket.send_text(message)
                except:
                    self.websocket_connections.remove(websocket)

# Глобальный экземпляр автоответчика
auto_responder = TelegramAutoResponder()

# FastAPI приложение
app = FastAPI(title="Telegram Auto-Responder API")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.on_event("startup")
async def startup_event():
    """Запуск при старте приложения"""
    await auto_responder.start_client()

@app.get("/api/chats")
async def get_chats():
    """Получение списка чатов"""
    return await auto_responder.get_chats()

@app.post("/api/chats/{chat_id}/toggle")
async def toggle_chat(chat_id: int, active: bool):
    """Включение/выключение автоответчика для чата"""
    await auto_responder.toggle_chat_autoresponder(chat_id, active)
    return {"success": True}

@app.get("/api/chats/{chat_id}/settings")
async def get_chat_settings(chat_id: int):
    """Получение настроек чата"""
    try:
        conn = sqlite3.connect('autoresponder.db')
        cursor = conn.cursor()
        cursor.execute(
            'SELECT welcome_message, system_prompt FROM chat_settings WHERE chat_id = ?',
            (chat_id,)
        )
        result = cursor.fetchone()
        conn.close()

        if result:
            return {
                "welcome_message": result[0] or AUTORESPONDER_CONFIG['default_welcome_message'],
                "system_prompt": result[1] or AUTORESPONDER_CONFIG['default_system_prompt']
            }
        else:
            return {
                "welcome_message": AUTORESPONDER_CONFIG['default_welcome_message'],
                "system_prompt": AUTORESPONDER_CONFIG['default_system_prompt']
            }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/chats/{chat_id}/settings")
async def update_chat_settings(chat_id: int, settings: dict):
    """Обновление настроек чата"""
    try:
        logger.info(f"💾 Сохраняем настройки для чата {chat_id}: {settings}")
        welcome_message = settings.get('welcome_message', AUTORESPONDER_CONFIG['default_welcome_message'])
        system_prompt = settings.get('system_prompt', AUTORESPONDER_CONFIG['default_system_prompt'])
        logger.info(f"💾 Приветственное сообщение: {welcome_message}")
        logger.info(f"💾 Системный промпт: {system_prompt}")

        conn = sqlite3.connect('autoresponder.db')
        cursor = conn.cursor()
        logger.info(f"💾 Выполняем SQL запрос для чата {chat_id}")
        cursor.execute(
            '''INSERT OR REPLACE INTO chat_settings
               (chat_id, welcome_message, system_prompt, is_active, first_message_sent)
               VALUES (?, ?, ?,
                       COALESCE((SELECT is_active FROM chat_settings WHERE chat_id = ?), FALSE),
                       FALSE)''',
            (chat_id, welcome_message, system_prompt, chat_id)
        )
        conn.commit()
        conn.close()
        logger.info(f"✅ Настройки для чата {chat_id} сохранены в базу данных")

        # Обновляем состояние в памяти
        auto_responder.first_message_sent[chat_id] = False

        return {"success": True}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/demo/simulate_message")
async def simulate_message(chat_id: int, message: str):
    """Демо-функция для симуляции получения сообщения (только в демо-режиме)"""
    if not auto_responder.demo_mode:
        raise HTTPException(status_code=400, detail="Доступно только в демо-режиме")

    # Проверяем, активен ли автоответчик для этого чата
    if chat_id not in auto_responder.active_chats or not auto_responder.active_chats[chat_id]:
        raise HTTPException(status_code=400, detail="Автоответчик не активен для этого чата")

    # Симулируем обработку сообщения
    await auto_responder.add_to_context(chat_id, message, is_outgoing=False)

    # Логируем входящее сообщение
    log_record = logger.makeRecord(
        logger.name, logging.INFO, __file__, 0,
        f"[ДЕМО] Получено сообщение: {message[:50]}{'...' if len(message) > 50 else ''}",
        (), None
    )
    log_record.chat_id = chat_id
    logger.handle(log_record)

    # Генерируем ответ
    response = await auto_responder.generate_response(chat_id, message)

    if response:
        # Сохраняем ответ в контекст
        await auto_responder.add_to_context(chat_id, response, is_outgoing=True)

        # Уведомляем веб-интерфейс
        await auto_responder.notify_websockets({
            'type': 'message_sent',
            'chat_id': chat_id,
            'message': response
        })

        # Логируем ответ
        log_record = logger.makeRecord(
            logger.name, logging.INFO, __file__, 0,
            f"[ДЕМО] Отправлен ответ: {response[:50]}{'...' if len(response) > 50 else ''}",
            (), None
        )
        log_record.chat_id = chat_id
        logger.handle(log_record)

    return {"status": "success", "response": response}

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket для real-time уведомлений"""
    await websocket.accept()
    auto_responder.websocket_connections.append(websocket)
    
    try:
        while True:
            await websocket.receive_text()
    except WebSocketDisconnect:
        auto_responder.websocket_connections.remove(websocket)

if __name__ == "__main__":
    uvicorn.run(app,
                host=SERVER_CONFIG['host'],
                port=SERVER_CONFIG['port'])
